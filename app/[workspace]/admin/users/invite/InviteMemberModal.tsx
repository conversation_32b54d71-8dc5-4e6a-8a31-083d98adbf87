'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Button, Input, message, Typography, Space, Divider } from 'antd';
import { CopyOutlined, ReloadOutlined, UserAddOutlined } from '@ant-design/icons';
import { getWorkspaceInvite, createOrRefreshInvite } from './actions';

const { Text, Title } = Typography;

interface InviteMemberModalProps {
  workspaceId: string;
  visible: boolean;
  onCancel: () => void;
}

interface InviteData {
  inviteCode: string;
  expiresAt: Date;
  createdAt: Date;
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({
  workspaceId,
  visible,
  onCancel
}) => {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [inviteData, setInviteData] = useState<InviteData | null>(null);

  // 生成邀请链接
  const generateInviteUrl = (inviteCode: string) => {
    if (typeof window !== 'undefined') {
      const protocol = window.location.protocol;
      const host = window.location.host;
      return `${protocol}//${host}/${workspaceId}/join/${inviteCode}`;
    }
    return '';
  };

  // 获取邀请码数据
  const fetchInviteData = async () => {
    setLoading(true);
    try {
      const result = await getWorkspaceInvite(workspaceId);
      if (result.status === 'success') {
        setInviteData({
          inviteCode: result.data.inviteCode,
          expiresAt: new Date(result.data.expiresAt),
          createdAt: new Date(result.data.createdAt)
        });
      } else {
        // 如果没有邀请码，自动创建一个
        await handleRefreshInvite();
      }
    } catch (error) {
      console.error('Failed to fetch invite data:', error);
      message.error('获取邀请信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新邀请码
  const handleRefreshInvite = async () => {
    setRefreshing(true);
    try {
      const result = await createOrRefreshInvite(workspaceId);
      if (result.status === 'success') {
        setInviteData({
          inviteCode: result.data.inviteCode,
          expiresAt: new Date(result.data.expiresAt),
          createdAt: new Date(result.data.createdAt)
        });
        message.success('邀请链接已刷新');
      } else {
        message.error(result.message || '刷新邀请链接失败');
      }
    } catch (error) {
      console.error('Failed to refresh invite:', error);
      message.error('刷新邀请链接失败');
    } finally {
      setRefreshing(false);
    }
  };

  // 复制邀请链接
  const handleCopyInviteUrl = async () => {
    if (!inviteData) return;
    
    const inviteUrl = generateInviteUrl(inviteData.inviteCode);
    try {
      await navigator.clipboard.writeText(inviteUrl);
      message.success('邀请链接已复制到剪贴板');
    } catch (error) {
      console.error('Failed to copy:', error);
      message.error('复制失败');
    }
  };

  // 格式化过期时间
  const formatExpiryTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 当Modal打开时获取邀请数据
  useEffect(() => {
    if (visible) {
      fetchInviteData();
    }
  }, [visible, workspaceId]);

  return (
    <Modal
      title={
        <Space>
          <UserAddOutlined />
          <span>邀请成员</span>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>
      ]}
      width={600}
    >
      <div style={{ padding: '16px 0' }}>
        <Text type="secondary">
          可通过下列任意方式邀请用户加入此空间，加入的用户自动归到「默认分组」
        </Text>
        
        <Divider />
        
        <div style={{ marginBottom: 24 }}>
          <Title level={5} style={{ marginBottom: 16 }}>邀请链接</Title>
          
          {loading ? (
            <div>加载中...</div>
          ) : inviteData ? (
            <Space direction="vertical" style={{ width: '100%' }}>
              <Input
                value={generateInviteUrl(inviteData.inviteCode)}
                readOnly
                style={{ 
                  backgroundColor: '#f5f5f5',
                  fontFamily: 'monospace',
                  fontSize: '14px'
                }}
                suffix={
                  <Button
                    type="text"
                    icon={<CopyOutlined />}
                    onClick={handleCopyInviteUrl}
                    size="small"
                  >
                    复制邀请链接
                  </Button>
                }
              />
              
              <div style={{ marginTop: 16 }}>
                <Space split={<Divider type="vertical" />}>
                  <Text type="secondary">
                    邀请有效期至：{formatExpiryTime(inviteData.expiresAt)}
                  </Text>
                  <Button
                    type="link"
                    icon={<ReloadOutlined />}
                    onClick={handleRefreshInvite}
                    loading={refreshing}
                    style={{ padding: 0 }}
                  >
                    点击刷新
                  </Button>
                </Space>
              </div>
            </Space>
          ) : (
            <div>
              <Text type="secondary">暂无邀请链接</Text>
              <Button
                type="primary"
                onClick={handleRefreshInvite}
                loading={refreshing}
                style={{ marginLeft: 16 }}
              >
                生成邀请链接
              </Button>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default InviteMemberModal;
