'use server';
import { groups, users, userWorkspace } from '@/app/db/schema';
import { db } from '@/app/db';
import { desc, eq, and } from 'drizzle-orm';
import { isWorkspaceAdmin } from '@/app/utils/workspace';
import bcrypt from "bcryptjs";

type UserActionParams = {
  email: string;
  password?: string;
  isAdmin: boolean;
  groupId?: string;
};

const handleDatabaseError = (error: unknown, defaultMessage: string) => ({
  success: false,
  message: error instanceof Error ? error.message : defaultMessage
});

const hashPassword = async (password: string) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

export async function getUserList(workspaceId: string, groupId?: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }

  try {
    // 构建查询条件
    let whereConditions = [
      eq(userWorkspace.workspaceId, workspaceId),
      eq(userWorkspace.isActive, true)
    ];

    // 如果指定了分组，添加分组过滤条件
    if (groupId && groupId !== '_all') {
      whereConditions.push(eq(userWorkspace.groupId, groupId));
    }

    // 查询用户工作空间关联信息，包含用户基本信息和用量数据
    const userWorkspaceList = await db.query.userWorkspace.findMany({
      where: and(...whereConditions),
      columns: {
        userId: true,
        groupId: true,
        role: true,
        todayTotalTokens: true,
        currentMonthTotalTokens: true,
        usageUpdatedAt: true,
        joinedAt: true,
      },
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          }
        },
        group: {
          columns: {
            id: true,
            name: true,
            tokenLimitType: true,
            monthlyTokenLimit: true,
          }
        }
      },
      orderBy: [desc(userWorkspace.joinedAt)]
    });

    if (userWorkspaceList.length === 0) {
      return [];
    }

    // 获取今天凌晨 0 点的时间戳
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

    // 处理每条记录，计算实际用量
    const result = userWorkspaceList.map(userWorkspaceItem => {
      return {
        id: userWorkspaceItem.user.id,
        name: userWorkspaceItem.user.name,
        email: userWorkspaceItem.user.email,
        role: userWorkspaceItem.role, // 使用 userWorkspace.role 而不是 user.isAdmin
        createdAt: userWorkspaceItem.user.createdAt,
        groupId: userWorkspaceItem.groupId,
        group: userWorkspaceItem.group, // 直接使用关联查询的结果
        todayTotalTokens: userWorkspaceItem.usageUpdatedAt && new Date(userWorkspaceItem.usageUpdatedAt) >= today
          ? userWorkspaceItem.todayTotalTokens
          : 0,
        currentMonthTotalTokens: userWorkspaceItem.usageUpdatedAt && new Date(userWorkspaceItem.usageUpdatedAt) >= firstDayOfMonth
          ? userWorkspaceItem.currentMonthTotalTokens
          : 0,
        usageUpdatedAt: userWorkspaceItem.usageUpdatedAt,
      };
    });

    return result;
  } catch (error) {
    throw new Error('Failed to fetch user list');
  }
}

export async function addUser(workspaceId: string, user: UserActionParams & { password: string }) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }
  try {
    const emailExists = await db.query.users.findFirst({
      where: eq(users.email, user.email),
    });
    if (emailExists) return { success: false, message: 'Email has been registered' }
    const hashedPassword = await hashPassword(user.password);

    // 验证分组是否属于当前workspace
    const group = user.groupId ? await db.query.groups.findFirst({
      where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
    }) : null;
    if (user.groupId && !group) {
      return {
        success: false,
        message: 'Group not found in this workspace'
      }
    }

    // 创建用户
    const [newUser] = await db.insert(users).values({
      email: user.email,
      password: hashedPassword,
      isAdmin: user.isAdmin,
    }).returning();

    // 将用户添加到workspace，并设置分组
    await db.insert(userWorkspace).values({
      userId: newUser.id,
      workspaceId: workspaceId,
      role: user.isAdmin ? 'admin' : 'member',
      groupId: user.groupId,
      isActive: true
    });

    return {
      success: true,
      message: 'User added successfully'
    }
  } catch (error) {
    return handleDatabaseError(error, 'User registration failed');
  }
}

export async function deleteUser(workspaceId: string, email: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  try {
    // 首先验证用户是否属于当前workspace
    const userInWorkspace = await db.select({
      userId: users.id
    }).from(users)
      .innerJoin(userWorkspace, eq(users.id, userWorkspace.userId))
      .where(and(
        eq(users.email, email),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ));

    if (userInWorkspace.length === 0) {
      return {
        success: false,
        message: 'User not found in this workspace'
      }
    }

    // 从workspace中移除用户（软删除）
    await db.update(userWorkspace).set({
      isActive: false
    }).where(and(
      eq(userWorkspace.userId, userInWorkspace[0].userId),
      eq(userWorkspace.workspaceId, workspaceId)
    ));

    return {
      success: true,
      message: 'User removed from workspace successfully'
    }
  } catch (error) {
    return {
      success: false,
      message: 'User delete failed'
    }
  }
}

export async function updateUser(workspaceId: string, email: string, user: UserActionParams) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }
  try {
    // 验证用户是否属于当前workspace
    const userInWorkspace = await db.select({
      userId: users.id,
      role: userWorkspace.role
    }).from(users)
      .innerJoin(userWorkspace, eq(users.id, userWorkspace.userId))
      .where(and(
        eq(users.email, email),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ));

    if (userInWorkspace.length === 0) {
      return {
        success: false,
        message: 'User not found in this workspace'
      }
    }

    // 验证分组是否属于当前workspace
    if (user.groupId) {
      const group = await db.query.groups.findFirst({
        where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
      });
      if (!group) {
        return {
          success: false,
          message: 'Group not found in this workspace'
        }
      }
    }

    // 更新workspace中的角色和分组
    let newRole = 'member';
    if(userInWorkspace[0].role === 'owner') {
      newRole = 'owner';
    } else {
      newRole = user.isAdmin ? 'admin' : 'member';
    }
    await db.update(userWorkspace).set({
      role: newRole as "admin" | "member" | "owner",
      groupId: user.groupId
    }).where(and(
      eq(userWorkspace.userId, userInWorkspace[0].userId),
      eq(userWorkspace.workspaceId, workspaceId)
    ));

    return {
      success: true,
      message: 'User updated successfully'
    }
  } catch (error) {
    return handleDatabaseError(error, 'User update failed');
  }
}