'use server';
import { db } from '@/app/db';
import { auth } from "@/auth";
import { eq, and, sql, inArray, asc } from 'drizzle-orm';

import { workspaces, userWorkspace, groups, llmSettingsTable, llmModels, users } from '@/app/db/schema';

// 导入种子数据
import providers from '@/app/db/data/providers';
import { modelList as OpenaiModels } from "@/app/db/data/models/openai";
import { modelList as ClaudeModels } from "@/app/db/data/models/claude";
import { modelList as GeminiModels } from "@/app/db/data/models/gemini";
import { modelList as MoonshotModels } from "@/app/db/data/models/moonshot";
import { modelList as QwenModels } from "@/app/db/data/models/qwen";
import { modelList as VolcengineModels } from "@/app/db/data/models/volcengine";
import { modelList as DeepseekModels } from "@/app/db/data/models/deepseek";
import { modelList as QianfanModels } from "@/app/db/data/models/qianfan";
import { modelList as SiliconflowModels } from "@/app/db/data/models/siliconflow";
import { modelList as OllamaModels } from "@/app/db/data/models/ollama";
import { modelList as OpenrouterModels } from "@/app/db/data/models/openrouter";
import { modelList as ZhipuModels } from "@/app/db/data/models/zhipu";
import { modelList as GrokModels } from "@/app/db/data/models/grok";
import { modelList as HunyuanModels } from "@/app/db/data/models/hunyuan";

// 合并所有模型数据
const allModelList = [
  ...OpenaiModels,
  ...ClaudeModels,
  ...GeminiModels,
  ...MoonshotModels,
  ...QwenModels,
  ...VolcengineModels,
  ...DeepseekModels,
  ...QianfanModels,
  ...SiliconflowModels,
  ...OllamaModels,
  ...OpenrouterModels,
  ...ZhipuModels,
  ...GrokModels,
  ...HunyuanModels,
];

export interface CreateWorkspaceParams {
  name: string;
  id: string;
}

export interface WorkspaceInfo {
  id: string;
  name: string;
  owner: string;
  plan: 'free' | 'plus' | 'pro';
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 创建新的 workspace
 */
export const createWorkspace = async (params: CreateWorkspaceParams) => {
  const session = await auth();
  if (!session?.user.id) {
    return {
      status: 'fail',
      message: 'Please login first.'
    };
  }

  try {
    // 检查用户已创建的工作空间数量
    const maxWorkspaces = parseInt(process.env.MAX_WORKSPACES_PER_USER || '1');
    const userWorkspaces = await db.query.userWorkspace.findMany({
      where: and(
        eq(userWorkspace.userId, session.user.id),
        eq(userWorkspace.isActive, true)
      )
    });

    if (userWorkspaces.length >= maxWorkspaces) {
      return {
        status: 'fail',
        message: `公测期间一个用户只能创建 ${maxWorkspaces} 个工作空间`
      };
    }

    // 生成 workspace ID，如果没有提供的话
    const workspaceId = params.id;

    // 检查 workspace ID 是否已存在
    const existingWorkspace = await db.query.workspaces.findFirst({
      where: eq(workspaces.id, workspaceId)
    });

    if (existingWorkspace) {
      return {
        status: 'fail',
        message: 'Workspace ID already exists.'
      };
    }

    // 使用事务确保所有操作要么全部成功，要么全部回滚
    const result = await db.transaction(async (tx) => {
      // 创建 workspace
      const [newWorkspace] = await tx.insert(workspaces)
        .values({
          id: workspaceId,
          name: params.name,
          owner: session.user.id,
          plan: 'free'
        })
        .returning();

      // 创建默认用户组
      const [defaultGroup] = await tx.insert(groups)
        .values({
          name: 'Default Group',
          workspaceId: workspaceId,
          isDefault: true
        })
        .returning();

      // 将当前用户（workspace 创建者）添加到工作空间，并设置为默认用户组
      await tx.insert(userWorkspace).values({
        userId: session.user.id,
        workspaceId: workspaceId,
        role: 'owner',
        groupId: defaultGroup.id,
        isActive: true
      });

      // 初始化默认的 providers
      const workspaceProviders = providers.map(provider => ({
        ...provider,
        apiStyle: provider.apiStyle as "openai" | "openai_response" | "claude" | "gemini",
        type: provider.type as "default" | "custom",
        workspaceId, // 替换为新的 workspaceId
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      await tx.insert(llmSettingsTable)
        .values(workspaceProviders)
        .onConflictDoNothing({
          target: [llmSettingsTable.provider, llmSettingsTable.workspaceId]
        });

      // 初始化默认的 models
      const workspaceModels = allModelList.map((model) => ({
        name: model.id,
        displayName: model.displayName,
        maxTokens: model.maxTokens,
        supportVision: model.supportVision,
        supportTool: model.supportTool,
        builtInImageGen: model.builtInImageGen,
        builtInWebSearch: model.builtInWebSearch,
        selected: model.selected,
        providerId: model.provider.id,
        providerName: model.provider.providerName,
        type: model.type ?? 'default',
        workspaceId, // 关联到新的 workspaceId
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      await tx.insert(llmModels)
        .values(workspaceModels)
        .onConflictDoNothing({
          target: [llmModels.name, llmModels.providerId, llmModels.workspaceId]
        });

      return newWorkspace;
    });

    return {
      status: 'success',
      data: {
        id: result.id,
        name: result.name,
        owner: result.owner,
        plan: result.plan,
        createdAt: result.createdAt!,
        updatedAt: result.updatedAt!
      } as WorkspaceInfo
    };
  } catch (error) {
    console.error('Create workspace error:', error);
    return {
      status: 'fail',
      message: 'Failed to create workspace.'
    };
  }
};

/**
 * 获取用户的 workspace 列表
 */
export const getUserWorkspaces = async () => {
  const session = await auth();
  if (!session?.user.id) {
    return {
      status: 'fail',
      message: 'Please login first.',
      data: []
    };
  }

  try {
    const userWorkspaces = await db.query.userWorkspace.findMany({
      where: and(
        eq(userWorkspace.userId, session.user.id),
        eq(userWorkspace.isActive, true)
      ),
      with: {
        workspace: true
      }
    });

    const workspaceList = userWorkspaces.map(uw => ({
      id: uw.workspace.id,
      name: uw.workspace.name,
      owner: uw.workspace.owner,
      plan: uw.workspace.plan,
      role: uw.role,
      createdAt: uw.workspace.createdAt!,
      updatedAt: uw.workspace.updatedAt!
    }));

    return {
      status: 'success',
      data: workspaceList
    };
  } catch (error) {
    console.error('Get user workspaces error:', error);
    return {
      status: 'fail',
      message: 'Failed to get workspaces.',
      data: []
    };
  }
};

/**
 * 获取用户的第一个 workspace（用于首页跳转）
 * 优化版本：只查询 userWorkspace 表，按加入时间排序
 */
export const getUserFirstWorkspace = async () => {
  const session = await auth();
  if (!session?.user.id) {
    return {
      status: 'fail',
      message: 'Please login first.',
      data: null
    };
  }

  try {
    // 只查询 userWorkspace 表，获取第一个工作区
    const firstUserWorkspace = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, session.user.id),
        eq(userWorkspace.isActive, true)
      ),
      orderBy: asc(userWorkspace.joinedAt), // 按加入时间排序，获取最早加入的工作区
      columns: {
        workspaceId: true,
        role: true,
        joinedAt: true
      }
    });

    if (!firstUserWorkspace) {
      return {
        status: 'success',
        data: null // 用户没有工作区
      };
    }

    return {
      status: 'success',
      data: {
        workspaceId: firstUserWorkspace.workspaceId,
        role: firstUserWorkspace.role,
        joinedAt: firstUserWorkspace.joinedAt
      }
    };
  } catch (error) {
    console.error('Get user first workspace error:', error);
    return {
      status: 'fail',
      message: 'Failed to get first workspace.',
      data: null
    };
  }
};

export const getWorkspacesWithUserCount = async () => {
  const session = await auth();
  if (!session?.user.id) {
    return {
      status: 'fail',
      message: 'Please login first.',
      data: []
    };
  }

  try {
    // 获取用户的工作区列表
    const userWorkspaces = await db.query.userWorkspace.findMany({
      where: and(
        eq(userWorkspace.userId, session.user.id),
        eq(userWorkspace.isActive, true)
      ),
      with: {
        workspace: true
      }
    });

    // 获取所有工作区的用户数量统计
    const workspaceIds = userWorkspaces.map(uw => uw.workspace.id);

    if (workspaceIds.length === 0) {
      return {
        status: 'success',
        data: []
      };
    }

    // 使用 SQL 聚合查询获取每个工作区的用户数量，避免 N+1 查询
    const userCountQuery = await db
      .select({
        workspaceId: userWorkspace.workspaceId,
        userCount: sql<number>`count(*)::int`
      })
      .from(userWorkspace)
      .where(
        and(
          inArray(userWorkspace.workspaceId, workspaceIds),
          eq(userWorkspace.isActive, true)
        )
      )
      .groupBy(userWorkspace.workspaceId);

    // 创建用户数量映射
    const userCountMap = new Map<string, number>();
    userCountQuery.forEach(item => {
      userCountMap.set(item.workspaceId, item.userCount);
    });

    const workspaceList = userWorkspaces.map(uw => ({
      id: uw.workspace.id,
      name: uw.workspace.name,
      owner: uw.workspace.owner,
      plan: uw.workspace.plan,
      role: uw.role,
      userCount: userCountMap.get(uw.workspace.id) || 0,
      createdAt: uw.workspace.createdAt!,
      updatedAt: uw.workspace.updatedAt!
    }));

    return {
      status: 'success',
      data: workspaceList
    };
  } catch (error) {
    console.error('Get user workspaces error:', error);
    return {
      status: 'fail',
      message: 'Failed to get workspaces.',
      data: []
    };
  }
};
/**
 * 获取 workspace 详情
 */
export const getWorkspaceInfo = async (workspaceId: string) => {
  const session = await auth();
  if (!session?.user.id) {
    return {
      status: 'fail',
      message: 'Please login first.',
      data: null
    };
  }

  try {
    // 检查用户是否有权限访问该 workspace
    const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, session.user.id),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ),
      with: {
        workspace: true
      }
    });

    if (!userWorkspaceRecord) {
      return {
        status: 'fail',
        message: 'Workspace not found or access denied.',
        data: null
      };
    }

    return {
      status: 'success',
      data: {
        id: userWorkspaceRecord.workspace.id,
        name: userWorkspaceRecord.workspace.name,
        owner: userWorkspaceRecord.workspace.owner,
        plan: userWorkspaceRecord.workspace.plan,
        role: userWorkspaceRecord.role,
        createdAt: userWorkspaceRecord.workspace.createdAt!,
        updatedAt: userWorkspaceRecord.workspace.updatedAt!
      }
    };
  } catch (error) {
    console.error('Get workspace info error:', error);
    return {
      status: 'fail',
      message: 'Failed to get workspace info.',
      data: null
    };
  }
};

/**
 * 检查 workspace ID 是否可用
 */
export const checkWorkspaceIdAvailability = async (workspaceId: string) => {
  try {
    const existingWorkspace = await db.query.workspaces.findFirst({
      where: eq(workspaces.id, workspaceId)
    });

    return {
      status: 'success',
      available: !existingWorkspace
    };
  } catch (error) {
    console.error('Check workspace ID availability error:', error);
    return {
      status: 'fail',
      available: false
    };
  }
};
