'use server';
import bcrypt from "bcryptjs";
import { eq } from 'drizzle-orm';
import { users } from '@/app/db/schema';
import { db } from '@/app/db';
import { signIn } from '@/auth';

export async function register(email: string, password: string) {
  try {
    const user = await db.query.users
      .findFirst({
        where: eq(users.email, email)
      })
    if (user) {
      return {
        status: 'fail',
        message: '该邮箱已被注册',
      };
    }
    // 生成盐值 (salt)，指定盐值的回合次数（通常是 10）
    const salt = await bcrypt.genSalt(10);

    // 使用盐值对密码进行哈希处理
    const hashedPassword = await bcrypt.hash(password, salt);
    // 将新用户数据插入数据库
    const result = await db.insert(users).values({
      email,
      password: hashedPassword,
    });
    // 注册成功后，自动登录
    const signInResponse = await signIn("credentials", {
      redirect: false, // 不跳转页面
      email,
      password,
    });
    // 返回成功消息或其他所需数据
    return {
      status: 'success',
    }
  } catch (error) {
    console.log(error)
    throw new Error('用户注册失败，请稍后再试');
  }
}

export async function getActiveAuthProvides() {
  const activeAuthProvides = [];
  // 兼容历史版本，只要没配置 OFF，就默认启用 Email 登录
  if (!process.env.EMAIL_AUTH_STATUS || (process.env.EMAIL_AUTH_STATUS.toLowerCase() !== 'off')) {
    activeAuthProvides.push('email')
  }
  if (process.env.GITHUB_AUTH_STATUS && process.env.GITHUB_AUTH_STATUS.toLowerCase() === 'on') {
    activeAuthProvides.push('github')
  }
  if (process.env.GOOGLE_AUTH_STATUS && process.env.GOOGLE_AUTH_STATUS.toLowerCase() === 'on') {
    activeAuthProvides.push('google')
  }
  return activeAuthProvides;
}
